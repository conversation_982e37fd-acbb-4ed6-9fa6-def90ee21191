{"avg_transactions_per_day": {"min": 1, "max": 4}, "date_wt": {"day_of_week": {"monday": 80, "tuesday": 90, "wednesday": 100, "thursday": 100, "friday": 125, "saturday": 150, "sunday": 150}, "time_of_year": {"holidays": {"start_date (MM-DD)": "11-30", "end_date (MM-DD)": "12-31", "weight": 200}, "post_holidays": {"start_date (MM-DD)": "1-1", "end_date (MM-DD)": "02-28", "weight": 75}, "summer": {"start_date (MM-DD)": "05-24", "end_date (MM-DD)": "09-01", "weight": 125}}, "year": {"0": 100, "1": 100, "2": 100, "3": 100}}, "categories_wt": {"gas_transport": 50, "grocery_net": 50, "grocery_pos": 150, "misc_net": 30, "misc_pos": 100, "shopping_net": 100, "shopping_pos": 100, "entertainment": 105, "food_dining": 100, "health_fitness": 90, "home": 150, "kids_pets": 110, "personal_care": 50, "travel": 45}, "categories_amt": {"gas_transport": {"mean": 70, "stdev": 5}, "grocery_net": {"mean": 50, "stdev": 5}, "grocery_pos": {"mean": 100, "stdev": 10}, "misc_net": {"mean": 100, "stdev": 20}, "misc_pos": {"mean": 35, "stdev": 5}, "shopping_net": {"mean": 95, "stdev": 10}, "shopping_pos": {"mean": 135, "stdev": 12}, "entertainment": {"mean": 75, "stdev": 7}, "food_dining": {"mean": 60, "stdev": 6}, "health_fitness": {"mean": 75, "stdev": 2}, "home": {"mean": 80, "stdev": 8}, "kids_pets": {"mean": 50, "stdev": 5}, "personal_care": {"mean": 75, "stdev": 5}, "travel": {"mean": 500, "stdev": 50}}, "shopping_time": {"AM": 45, "PM": 55}, "travel_pct": 35, "travel_max_dist": 1200}