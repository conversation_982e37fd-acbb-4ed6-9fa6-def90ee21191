{"avg_transactions_per_day": {"min": 0, "max": 6}, "date_wt": {"day_of_week": {"monday": 80, "tuesday": 80, "wednesday": 80, "thursday": 80, "friday": 200, "saturday": 250, "sunday": 150}, "time_of_year": {"holidays": {"start_date (MM-DD)": "11-30", "end_date (MM-DD)": "12-31", "weight": 200}, "post_holidays": {"start_date (MM-DD)": "1-1", "end_date (MM-DD)": "02-28", "weight": 75}, "summer": {"start_date (MM-DD)": "05-24", "end_date (MM-DD)": "09-01", "weight": 125}}, "year": {"0": 100, "1": 120, "2": 140, "3": 160}}, "categories_wt": {"gas_transport": 150, "grocery_net": 80, "grocery_pos": 50, "misc_net": 100, "misc_pos": 100, "shopping_net": 100, "shopping_pos": 100, "entertainment": 130, "food_dining": 140, "health_fitness": 110, "home": 50, "kids_pets": 75, "personal_care": 100, "travel": 35}, "categories_amt": {"gas_transport": {"mean": 60, "stdev": 15}, "grocery_net": {"mean": 30, "stdev": 20}, "grocery_pos": {"mean": 60, "stdev": 20}, "misc_net": {"mean": 120, "stdev": 60}, "misc_pos": {"mean": 140, "stdev": 90}, "shopping_net": {"mean": 90, "stdev": 120}, "shopping_pos": {"mean": 150, "stdev": 35}, "entertainment": {"mean": 90, "stdev": 30}, "food_dining": {"mean": 20, "stdev": 50}, "health_fitness": {"mean": 40, "stdev": 50}, "home": {"mean": 40, "stdev": 50}, "kids_pets": {"mean": 50, "stdev": 50}, "personal_care": {"mean": 35, "stdev": 50}, "travel": {"mean": 40, "stdev": 600}}, "shopping_time": {"AM": 20, "PM": 80}, "travel_pct": 10, "travel_max_dist": 500}