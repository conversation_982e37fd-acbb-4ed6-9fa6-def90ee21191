#!/usr/bin/env python3
"""
Script to normalize transaction CSV files by removing redundant customer data.
This script processes all transaction files and keeps only essential transaction columns.
"""

import pandas as pd
import os
import glob
import sys
from datetime import datetime

data_dir = "data_1010"

def normalize_transaction_files():
    """Normalize all transaction files by removing redundant customer columns."""
    
    print("=" * 80)
    print("SPARKOV DATA NORMALIZATION SCRIPT")
    print("=" * 80)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
  
    
    # Get all transaction files (exclude customers.csv and merchants.csv)
    all_files = glob.glob(os.path.join(data_dir, "*.csv"))
    transaction_files = [f for f in all_files if not f.endswith(('customers.csv', 'merchants.csv'))]
    
    print(f"\nFound {len(transaction_files)} transaction files to normalize")
    
    # Define columns to keep in normalized transaction files
    columns_to_keep = [
        'ssn',           # Foreign key to customers
        'trans_num',     # Transaction ID
        'trans_date',    # Transaction date
        'trans_time',    # Transaction time
        'unix_time',     # Unix timestamp
        'category',      # Transaction category
        'amt',           # Transaction amount
        'is_fraud',      # Fraud flag
        'merchant',      # Merchant name (FK to merchants)
        'merch_lat',     # Merchant latitude
        'merch_long'     # Merchant longitude
    ]
    
    # Define columns to remove (redundant customer data)
    columns_to_remove = [
        'cc_num', 'first', 'last', 'gender', 'street', 'city', 'state', 'zip',
        'lat', 'long', 'city_pop', 'job', 'dob', 'acct_num', 'profile'
    ]
    
    print(f"\nColumns to keep: {', '.join(columns_to_keep)}")
    print(f"Columns to remove: {', '.join(columns_to_remove)}")
    
    # Process statistics
    total_files = len(transaction_files)
    processed_files = 0
    total_records_processed = 0
    total_size_before = 0
    total_size_after = 0
    errors = []
    
    print(f"\nProcessing {total_files} files...")
    print("-" * 80)
    
    for i, file_path in enumerate(transaction_files, 1):
        filename = os.path.basename(file_path)
        
        try:
            # Get original file size
            original_size = os.path.getsize(file_path)
            total_size_before += original_size
            
            # Read the file
            print(f"[{i:3d}/{total_files}] Processing {filename}...", end=" ")
            df = pd.read_csv(file_path, sep='|')
            
            # Verify expected columns exist
            missing_columns = [col for col in columns_to_keep if col not in df.columns]
            if missing_columns:
                print(f"ERROR - Missing columns: {missing_columns}")
                errors.append(f"{filename}: Missing columns {missing_columns}")
                continue
            
            # Create normalized dataframe with only the columns we want to keep
            normalized_df = df[columns_to_keep].copy()
            
            # Write the normalized file back (overwriting original)
            normalized_df.to_csv(file_path, sep='|', index=False)
            
            # Get new file size
            new_size = os.path.getsize(file_path)
            total_size_after += new_size
            
            # Update statistics
            processed_files += 1
            total_records_processed += len(df)
            
            # Calculate space savings for this file
            size_reduction = original_size - new_size
            reduction_percent = (size_reduction / original_size) * 100
            
            print(f"✓ {len(df):,} records, {reduction_percent:.1f}% smaller")
            
            # Progress indicator
            if i % 50 == 0:
                print(f"\nProgress: {i}/{total_files} files processed ({(i/total_files)*100:.1f}%)")
                print("-" * 80)
                
        except Exception as e:
            print(f"ERROR - {str(e)}")
            errors.append(f"{filename}: {str(e)}")
            continue
    
    # Final statistics
    print("\n" + "=" * 80)
    print("NORMALIZATION COMPLETE")
    print("=" * 80)
    
    print(f"\nSUMMARY:")
    print(f"- Files processed successfully: {processed_files}/{total_files}")
    print(f"- Total records processed: {total_records_processed:,}")
    print(f"- Original total size: {total_size_before / (1024*1024):.2f} MB")
    print(f"- New total size: {total_size_after / (1024*1024):.2f} MB")
    print(f"- Space saved: {(total_size_before - total_size_after) / (1024*1024):.2f} MB")
    print(f"- Overall reduction: {((total_size_before - total_size_after) / total_size_before) * 100:.1f}%")
    
    if errors:
        print(f"\nERRORS ENCOUNTERED ({len(errors)}):")
        for error in errors:
            print(f"  - {error}")
    else:
        print(f"\n✓ All files processed successfully!")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return processed_files, total_records_processed, errors

def verify_normalization():
    """Verify that normalization was successful by checking a sample file."""
    
    print("\n" + "=" * 80)
    print("VERIFICATION")
    print("=" * 80)
    
    # Get all transaction files (exclude customers.csv and merchants.csv)
    transaction_files = glob.glob(os.path.join(data_dir, "*.csv"))
    transaction_files = [f for f in transaction_files if not f.endswith(('customers.csv', 'merchants.csv'))]
    
    if not transaction_files:
        print("No transaction files found for verification")
        return
    
    # Check first transaction file
    sample_file = transaction_files[0]
    filename = os.path.basename(sample_file)
    
    try:
        df = pd.read_csv(sample_file, sep='|')
        print(f"\nSample file: {filename}")
        print(f"Columns ({len(df.columns)}): {', '.join(df.columns)}")
        print(f"Records: {len(df):,}")
        
        # Expected columns after normalization
        expected_columns = [
            'ssn', 'trans_num', 'trans_date', 'trans_time', 'unix_time',
            'category', 'amt', 'is_fraud', 'merchant', 'merch_lat', 'merch_long'
        ]
        
        # Check if normalization was successful
        if set(df.columns) == set(expected_columns):
            print("✓ Normalization successful - only essential columns remain")
        else:
            missing = set(expected_columns) - set(df.columns)
            extra = set(df.columns) - set(expected_columns)
            if missing:
                print(f"⚠ Missing expected columns: {missing}")
            if extra:
                print(f"⚠ Unexpected columns found: {extra}")
        
        # Show sample data
        print(f"\nSample data (first 3 rows):")
        print(df.head(3).to_string(index=False))
        
    except Exception as e:
        print(f"Error verifying {filename}: {e}")

def main():
    """Main function to run the normalization process."""
    
    # Check if data directory exists
    if not os.path.exists(data_dir):
        print("Error: 'data' directory not found")
        sys.exit(1)
    
    # Run normalization
    processed_files, total_records, errors = normalize_transaction_files()
    
    # Verify results
    verify_normalization()
    
    # Final message
    if errors:
        print(f"\n⚠ Normalization completed with {len(errors)} errors")
        sys.exit(1)
    else:
        print(f"\n🎉 Normalization completed successfully!")
        print(f"   {processed_files} files normalized")
        print(f"   {total_records:,} transaction records processed")

if __name__ == "__main__":
    main()
