{"avg_transactions_per_day": {"min": 1, "max": 4}, "date_wt": {"day_of_week": {"monday": 80, "tuesday": 90, "wednesday": 100, "thursday": 100, "friday": 150, "saturday": 175, "sunday": 175}, "time_of_year": {"holidays": {"start_date (MM-DD)": "11-30", "end_date (MM-DD)": "12-31", "weight": 200}, "post_holidays": {"start_date (MM-DD)": "1-1", "end_date (MM-DD)": "02-28", "weight": 75}, "summer": {"start_date (MM-DD)": "05-24", "end_date (MM-DD)": "09-01", "weight": 125}}, "year": {"0": 100, "1": 100, "2": 100, "3": 100}}, "categories_wt": {"gas_transport": 225, "grocery_net": 15, "grocery_pos": 150, "misc_net": 100, "misc_pos": 65, "shopping_net": 125, "shopping_pos": 110, "entertainment": 120, "food_dining": 90, "health_fitness": 125, "home": 150, "kids_pets": 150, "personal_care": 120, "travel": 60}, "categories_amt": {"gas_transport": {"mean": 60, "stdev": 15}, "grocery_net": {"mean": 50, "stdev": 20}, "grocery_pos": {"mean": 130, "stdev": 20}, "misc_net": {"mean": 60, "stdev": 150}, "misc_pos": {"mean": 50, "stdev": 150}, "shopping_net": {"mean": 50, "stdev": 150}, "shopping_pos": {"mean": 50, "stdev": 150}, "entertainment": {"mean": 50, "stdev": 50}, "food_dining": {"mean": 50, "stdev": 50}, "health_fitness": {"mean": 50, "stdev": 50}, "home": {"mean": 50, "stdev": 50}, "kids_pets": {"mean": 50, "stdev": 50}, "personal_care": {"mean": 50, "stdev": 50}, "travel": {"mean": 60, "stdev": 600}}, "shopping_time": {"AM": 50, "PM": 50}, "travel_pct": 30, "travel_max_dist": 1000}