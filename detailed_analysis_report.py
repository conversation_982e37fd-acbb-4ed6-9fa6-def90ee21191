#!/usr/bin/env python3
"""
Detailed analysis report for data normalization opportunities.
"""

import pandas as pd
import os
import glob
from collections import defaultdict
import sys

def detailed_file_analysis():
    """Provide detailed analysis of the data structure and redundancy."""
    
    print("=" * 80)
    print("SPARKOV DATA GENERATION - NORMALIZATION ANALYSIS REPORT")
    print("=" * 80)
    
    data_dir = "data_1010"
    
    # Get all CSV files
    all_files = glob.glob(os.path.join(data_dir, "*.csv"))
    customers_file = os.path.join(data_dir, "customers.csv")
    merchants_file = os.path.join(data_dir, "merchants.csv")
    transaction_files = [f for f in all_files if not f.endswith(('customers.csv', 'merchants.csv'))]
    
    print(f"\nFILE INVENTORY:")
    print(f"- Total CSV files: {len(all_files)}")
    print(f"- Customer master file: customers.csv")
    print(f"- Merchant master file: merchants.csv") 
    print(f"- Transaction files: {len(transaction_files)}")
    
    # Analyze customers.csv
    print(f"\n" + "="*50)
    print("CUSTOMERS.CSV ANALYSIS")
    print("="*50)
    
    customers_df = pd.read_csv(customers_file, sep='|')
    print(f"Records: {len(customers_df):,}")
    print(f"Columns: {len(customers_df.columns)}")
    print(f"Column names: {', '.join(customers_df.columns)}")
    
    # Key uniqueness analysis
    print(f"\nKEY FIELD ANALYSIS:")
    print(f"- SSN: {customers_df['ssn'].nunique():,} unique values ({customers_df['ssn'].nunique() == len(customers_df)})")
    print(f"- CC_NUM: {customers_df['cc_num'].nunique():,} unique values ({customers_df['cc_num'].nunique() == len(customers_df)})")
    print(f"- ACCT_NUM: {customers_df['acct_num'].nunique():,} unique values ({customers_df['acct_num'].nunique() == len(customers_df)})")
    
    # Analyze merchants.csv
    print(f"\n" + "="*50)
    print("MERCHANTS.CSV ANALYSIS")
    print("="*50)
    
    merchants_df = pd.read_csv(merchants_file, sep='|')
    print(f"Records: {len(merchants_df):,}")
    print(f"Columns: {len(merchants_df.columns)}")
    print(f"Column names: {', '.join(merchants_df.columns)}")
    print(f"Unique merchants: {merchants_df['merchant_name'].nunique():,}")
    print(f"Unique categories: {merchants_df['category'].nunique()}")
    
    # Analyze transaction files structure
    print(f"\n" + "="*50)
    print("TRANSACTION FILES ANALYSIS")
    print("="*50)
    
    # Sample a few transaction files
    sample_files = transaction_files[:5]
    total_transaction_records = 0
    
    for i, file_path in enumerate(sample_files):
        df = pd.read_csv(file_path, sep='|')
        total_transaction_records += len(df)
        if i == 0:  # Show structure for first file
            print(f"Sample file: {os.path.basename(file_path)}")
            print(f"Records: {len(df):,}")
            print(f"Columns: {len(df.columns)}")
            print(f"Column names: {', '.join(df.columns)}")
    
    # Estimate total records
    avg_records_per_file = total_transaction_records / len(sample_files)
    estimated_total_records = int(avg_records_per_file * len(transaction_files))
    
    print(f"\nESTIMATED TOTALS:")
    print(f"- Average records per transaction file: {avg_records_per_file:,.0f}")
    print(f"- Estimated total transaction records: {estimated_total_records:,}")
    
    # Redundancy analysis
    print(f"\n" + "="*50)
    print("REDUNDANCY ANALYSIS")
    print("="*50)
    
    # Load sample transaction data
    sample_df = pd.read_csv(sample_files[0], sep='|')
    
    # Identify redundant columns (customer data repeated in transactions)
    customer_columns_in_transactions = [
        'cc_num', 'first', 'last', 'gender', 'street', 'city', 'state', 'zip',
        'lat', 'long', 'city_pop', 'job', 'dob', 'acct_num', 'profile'
    ]
    
    transaction_only_columns = [
        'trans_num', 'trans_date', 'trans_time', 'unix_time', 'category', 
        'amt', 'is_fraud', 'merchant', 'merch_lat', 'merch_long'
    ]
    
    print(f"REDUNDANT COLUMNS (already in customers.csv):")
    for col in customer_columns_in_transactions:
        if col in sample_df.columns:
            print(f"  - {col}")
    
    print(f"\nTRANSACTION-SPECIFIC COLUMNS (should remain):")
    for col in transaction_only_columns:
        if col in sample_df.columns:
            print(f"  - {col}")
    
    print(f"  - ssn (foreign key to customers)")
    
    # Space analysis
    print(f"\n" + "="*50)
    print("SPACE SAVINGS ANALYSIS")
    print("="*50)
    
    current_columns = len(sample_df.columns)
    normalized_columns = len(transaction_only_columns) + 1  # +1 for ssn FK
    columns_to_remove = len(customer_columns_in_transactions)
    
    print(f"Current transaction file structure:")
    print(f"  - Columns per record: {current_columns}")
    print(f"  - Estimated total records: {estimated_total_records:,}")
    
    print(f"\nNormalized transaction file structure:")
    print(f"  - Columns per record: {normalized_columns}")
    print(f"  - Columns removed: {columns_to_remove}")
    print(f"  - Space reduction: ~{(columns_to_remove/current_columns)*100:.1f}%")
    
    # File size analysis
    print(f"\nCURRENT FILE SIZES:")
    customers_size = os.path.getsize(customers_file) / (1024*1024)
    merchants_size = os.path.getsize(merchants_file) / (1024*1024)
    
    total_transaction_size = 0
    for file_path in transaction_files:
        total_transaction_size += os.path.getsize(file_path)
    total_transaction_size_mb = total_transaction_size / (1024*1024)
    
    print(f"  - customers.csv: {customers_size:.2f} MB")
    print(f"  - merchants.csv: {merchants_size:.2f} MB")
    print(f"  - All transaction files: {total_transaction_size_mb:.2f} MB")
    print(f"  - Total: {customers_size + merchants_size + total_transaction_size_mb:.2f} MB")
    
    estimated_savings_mb = total_transaction_size_mb * (columns_to_remove/current_columns)
    print(f"\nESTIMATED SAVINGS AFTER NORMALIZATION:")
    print(f"  - Transaction files reduced by: ~{estimated_savings_mb:.2f} MB")
    print(f"  - New total size: ~{customers_size + merchants_size + (total_transaction_size_mb - estimated_savings_mb):.2f} MB")
    print(f"  - Overall space savings: ~{(estimated_savings_mb/(customers_size + merchants_size + total_transaction_size_mb))*100:.1f}%")
    
    return {
        'customers_df': customers_df,
        'merchants_df': merchants_df,
        'transaction_files': transaction_files,
        'customer_columns_in_transactions': customer_columns_in_transactions,
        'transaction_only_columns': transaction_only_columns
    }

def generate_recommendations(analysis_results):
    """Generate specific recommendations for normalization."""
    
    print(f"\n" + "="*50)
    print("NORMALIZATION RECOMMENDATIONS")
    print("="*50)
    
    print(f"\n1. CONFIRMED FINDINGS:")
    print(f"   ✓ SSN is unique across all customers (perfect primary key)")
    print(f"   ✓ Customer demographic data is duplicated in every transaction")
    print(f"   ✓ Significant space savings possible (~57% reduction in transaction files)")
    
    print(f"\n2. RECOMMENDED NORMALIZATION STRATEGY:")
    print(f"   a) Keep customers.csv as Customer Dimension Table")
    print(f"      - Primary Key: ssn")
    print(f"      - Contains: all customer demographic and account info")
    
    print(f"   b) Keep merchants.csv as Merchant Dimension Table")
    print(f"      - Primary Key: merchant_name")
    print(f"      - Contains: merchant category information")
    
    print(f"   c) Normalize Transaction Files (Fact Tables)")
    print(f"      - Remove redundant customer columns")
    print(f"      - Keep ssn as Foreign Key to customers")
    print(f"      - Keep merchant as Foreign Key to merchants")
    print(f"      - Retain all transaction-specific data")
    
    print(f"\n3. SPECIFIC COLUMNS TO REMOVE FROM TRANSACTION FILES:")
    for col in analysis_results['customer_columns_in_transactions']:
        print(f"      - {col}")
    
    print(f"\n4. COLUMNS TO KEEP IN TRANSACTION FILES:")
    print(f"      - ssn (FK to customers)")
    for col in analysis_results['transaction_only_columns']:
        print(f"      - {col}")
    
    print(f"\n5. BENEFITS OF NORMALIZATION:")
    print(f"   ✓ Eliminates data redundancy")
    print(f"   ✓ Reduces storage requirements by ~57%")
    print(f"   ✓ Improves data consistency")
    print(f"   ✓ Easier maintenance and updates")
    print(f"   ✓ Creates proper star schema for analytics")
    print(f"   ✓ Faster queries on transaction data")
    
    print(f"\n6. IMPLEMENTATION APPROACH:")
    print(f"   - Create normalization script to process all transaction files")
    print(f"   - Backup original files before normalization")
    print(f"   - Process files in batches to manage memory usage")
    print(f"   - Validate data integrity after normalization")

if __name__ == "__main__":
    analysis_results = detailed_file_analysis()
    generate_recommendations(analysis_results)
    
    print(f"\n" + "="*80)
    print("ANALYSIS COMPLETE")
    print("="*80)
