{"avg_transactions_per_day": {"min": 1, "max": 5}, "date_wt": {"day_of_week": {"monday": 80, "tuesday": 90, "wednesday": 100, "thursday": 100, "friday": 125, "saturday": 150, "sunday": 150}, "time_of_year": {"holidays": {"start_date (MM-DD)": "11-30", "end_date (MM-DD)": "12-31", "weight": 200}, "post_holidays": {"start_date (MM-DD)": "1-1", "end_date (MM-DD)": "02-28", "weight": 75}, "summer": {"start_date (MM-DD)": "05-24", "end_date (MM-DD)": "09-01", "weight": 125}}, "year": {"0": 100, "1": 100, "2": 100, "3": 100}}, "categories_wt": {"gas_transport": 150, "grocery_net": 80, "grocery_pos": 150, "misc_net": 75, "misc_pos": 100, "shopping_net": 100, "shopping_pos": 125, "entertainment": 110, "food_dining": 110, "health_fitness": 100, "home": 150, "kids_pets": 110, "personal_care": 75, "travel": 45}, "categories_amt": {"gas_transport": {"mean": 70, "stdev": 15}, "grocery_net": {"mean": 50, "stdev": 20}, "grocery_pos": {"mean": 60, "stdev": 20}, "misc_net": {"mean": 50, "stdev": 150}, "misc_pos": {"mean": 60, "stdev": 150}, "shopping_net": {"mean": 60, "stdev": 300}, "shopping_pos": {"mean": 60, "stdev": 300}, "entertainment": {"mean": 60, "stdev": 75}, "food_dining": {"mean": 40, "stdev": 50}, "health_fitness": {"mean": 60, "stdev": 50}, "home": {"mean": 60, "stdev": 50}, "kids_pets": {"mean": 60, "stdev": 50}, "personal_care": {"mean": 35, "stdev": 50}, "travel": {"mean": 60, "stdev": 600}}, "shopping_time": {"AM": 30, "PM": 70}, "travel_pct": 35, "travel_max_dist": 850}