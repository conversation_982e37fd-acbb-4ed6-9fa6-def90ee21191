{"avg_transactions_per_day": {"min": 0, "max": 6}, "date_wt": {"day_of_week": {"monday": 60, "tuesday": 70, "wednesday": 80, "thursday": 90, "friday": 180, "saturday": 250, "sunday": 175}, "time_of_year": {"holidays": {"start_date (MM-DD)": "11-30", "end_date (MM-DD)": "12-31", "weight": 200}, "post_holidays": {"start_date (MM-DD)": "1-1", "end_date (MM-DD)": "02-28", "weight": 75}, "summer": {"start_date (MM-DD)": "05-24", "end_date (MM-DD)": "09-01", "weight": 125}}, "year": {"0": 100, "1": 100, "2": 100, "3": 100}}, "categories_wt": {"gas_transport": 200, "grocery_net": 60, "grocery_pos": 75, "misc_net": 80, "misc_pos": 100, "shopping_net": 125, "shopping_pos": 150, "entertainment": 120, "food_dining": 120, "health_fitness": 100, "home": 85, "kids_pets": 125, "personal_care": 100, "travel": 55}, "categories_amt": {"gas_transport": {"mean": 60, "stdev": 15}, "grocery_net": {"mean": 40, "stdev": 20}, "grocery_pos": {"mean": 70, "stdev": 20}, "misc_net": {"mean": 50, "stdev": 150}, "misc_pos": {"mean": 50, "stdev": 150}, "shopping_net": {"mean": 50, "stdev": 300}, "shopping_pos": {"mean": 50, "stdev": 300}, "entertainment": {"mean": 50, "stdev": 50}, "food_dining": {"mean": 30, "stdev": 50}, "health_fitness": {"mean": 50, "stdev": 50}, "home": {"mean": 50, "stdev": 50}, "kids_pets": {"mean": 50, "stdev": 50}, "personal_care": {"mean": 35, "stdev": 50}, "travel": {"mean": 50, "stdev": 600}}, "shopping_time": {"AM": 20, "PM": 80}, "travel_pct": 10, "travel_max_dist": 750}