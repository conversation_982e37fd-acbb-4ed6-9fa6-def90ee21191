{"avg_transactions_per_day": {"min": 2, "max": 3}, "date_wt": {"day_of_week": {"monday": 100, "tuesday": 100, "wednesday": 100, "thursday": 100, "friday": 100, "saturday": 100, "sunday": 100}, "time_of_year": {"holidays": {"start_date (MM-DD)": "11-30", "end_date (MM-DD)": "12-31", "weight": 100}, "post_holidays": {"start_date (MM-DD)": "1-1", "end_date (MM-DD)": "02-28", "weight": 100}, "summer": {"start_date (MM-DD)": "05-24", "end_date (MM-DD)": "09-01", "weight": 100}}, "year": {"0": 100, "1": 100, "2": 100}}, "categories_wt": {"gas_transport": 100, "grocery_net": 100, "grocery_pos": 100, "pharmacy": 100, "misc_net": 100, "misc_pos": 100, "shopping_net": 100, "shopping_pos": 100, "utilities": 100, "entertainment": 100, "food_dining": 100, "health_fitness": 100, "home": 100, "kids_pets": 100, "personal_care": 100, "travel": 100}, "categories_amt": {"gas_transport": {"mean": 60, "stdev": 15}, "grocery_net": {"mean": 40, "stdev": 20}, "grocery_pos": {"mean": 60, "stdev": 20}, "pharmacy": {"mean": 30, "stdev": 15}, "misc_net": {"mean": 50, "stdev": 150}, "misc_pos": {"mean": 50, "stdev": 150}, "shopping_net": {"mean": 50, "stdev": 150}, "shopping_pos": {"mean": 50, "stdev": 150}, "utilities": {"mean": 75, "stdev": 25}, "entertainment": {"mean": 50, "stdev": 50}, "food_dining": {"mean": 50, "stdev": 50}, "health_fitness": {"mean": 50, "stdev": 50}, "home": {"mean": 50, "stdev": 50}, "kids_pets": {"mean": 50, "stdev": 50}, "personal_care": {"mean": 50, "stdev": 50}, "travel": {"mean": 50, "stdev": 600}}, "shopping_time": {"AM": 50, "PM": 50}, "travel_pct": 50, "travel_max_dist": 1000}