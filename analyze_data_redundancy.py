#!/usr/bin/env python3
"""
<PERSON>ript to analyze CSV files for data redundancy and normalization opportunities.
"""

import pandas as pd
import os
import glob
from collections import defaultdict
import sys

def analyze_customers_file(customers_file):
    """Analyze the customers.csv file to understand its structure and uniqueness."""
    print("=== ANALYZING CUSTOMERS.CSV ===")
    
    if not os.path.exists(customers_file):
        print(f"Error: {customers_file} not found")
        return None
    
    df = pd.read_csv(customers_file, sep='|')
    print(f"Customers file shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    
    # Check uniqueness of key fields
    print(f"\nUniqueness analysis:")
    print(f"- SSN unique: {df['ssn'].nunique() == len(df)} ({df['ssn'].nunique()}/{len(df)})")
    print(f"- CC_NUM unique: {df['cc_num'].nunique() == len(df)} ({df['cc_num'].nunique()}/{len(df)})")
    print(f"- ACCT_NUM unique: {df['acct_num'].nunique() == len(df)} ({df['acct_num'].nunique()}/{len(df)})")
    
    # Check for duplicates
    duplicate_ssns = df[df.duplicated(subset=['ssn'], keep=False)]
    if not duplicate_ssns.empty:
        print(f"Found {len(duplicate_ssns)} rows with duplicate SSNs")
        print(duplicate_ssns[['ssn', 'first', 'last']].head())
    
    return df

def analyze_merchants_file(merchants_file):
    """Analyze the merchants.csv file to understand its structure."""
    print("\n=== ANALYZING MERCHANTS.CSV ===")
    
    if not os.path.exists(merchants_file):
        print(f"Error: {merchants_file} not found")
        return None
    
    df = pd.read_csv(merchants_file, sep='|')
    print(f"Merchants file shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    
    # Check uniqueness
    print(f"\nUniqueness analysis:")
    print(f"- MERCHANT_NAME unique: {df['merchant_name'].nunique() == len(df)} ({df['merchant_name'].nunique()}/{len(df)})")
    print(f"- Categories: {df['category'].nunique()} unique categories")
    print(f"- Category distribution:")
    print(df['category'].value_counts().head(10))
    
    return df

def analyze_transaction_files(data_dir):
    """Analyze transaction files to identify redundant data."""
    print("\n=== ANALYZING TRANSACTION FILES ===")
    
    # Find all transaction CSV files (exclude customers.csv and merchants.csv)
    pattern = os.path.join(data_dir, "*.csv")
    all_files = glob.glob(pattern)
    transaction_files = [f for f in all_files if not f.endswith(('customers.csv', 'merchants.csv'))]
    
    print(f"Found {len(transaction_files)} transaction files")
    
    if not transaction_files:
        print("No transaction files found")
        return
    
    # Analyze first few files to understand structure
    sample_files = transaction_files[:3]
    redundancy_analysis = defaultdict(set)
    
    for file_path in sample_files:
        print(f"\nAnalyzing: {os.path.basename(file_path)}")
        
        try:
            # Read first few rows to understand structure
            df = pd.read_csv(file_path, sep='|', nrows=1000)
            print(f"  Shape: {df.shape}")
            print(f"  Columns: {list(df.columns)}")
            
            # Check what data is repeated across rows for same customer
            if 'ssn' in df.columns:
                customer_groups = df.groupby('ssn')
                for ssn, group in customer_groups:
                    if len(group) > 1:  # Multiple transactions for same customer
                        # Check which columns have the same value for all transactions of this customer
                        for col in df.columns:
                            if col not in ['trans_num', 'trans_date', 'trans_time', 'unix_time', 'category', 'amt', 'is_fraud', 'merchant', 'merch_lat', 'merch_long']:
                                if group[col].nunique() == 1:  # Same value for all transactions
                                    redundancy_analysis[col].add(ssn)
                        break  # Just analyze first customer with multiple transactions
            
        except Exception as e:
            print(f"  Error reading {file_path}: {e}")
    
    # Report redundancy findings
    print(f"\n=== REDUNDANCY ANALYSIS ===")
    print("Columns that appear to be redundant in transaction files:")
    print("(These columns have the same value for all transactions of the same customer)")
    
    customer_related_columns = [
        'ssn', 'cc_num', 'first', 'last', 'gender', 'street', 'city', 'state', 'zip',
        'lat', 'long', 'city_pop', 'job', 'dob', 'acct_num', 'profile'
    ]
    
    for col in customer_related_columns:
        if col in redundancy_analysis:
            print(f"  - {col}: Redundant (customer demographic data)")
    
    return transaction_files

def calculate_space_savings(data_dir, customers_df):
    """Calculate potential space savings from normalization."""
    print(f"\n=== SPACE SAVINGS ANALYSIS ===")
    
    # Get sample transaction file to understand structure
    pattern = os.path.join(data_dir, "*.csv")
    all_files = glob.glob(pattern)
    transaction_files = [f for f in all_files if not f.endswith(('customers.csv', 'merchants.csv'))]
    
    if not transaction_files:
        return
    
    # Analyze one transaction file
    sample_file = transaction_files[0]
    df_sample = pd.read_csv(sample_file, sep='|', nrows=1000)
    
    # Columns that could be removed from transaction files (already in customers.csv)
    redundant_columns = [
        'cc_num', 'first', 'last', 'gender', 'street', 'city', 'state', 'zip',
        'lat', 'long', 'city_pop', 'job', 'dob', 'acct_num', 'profile'
    ]
    
    # Calculate approximate space usage
    total_transaction_files = len(transaction_files)
    avg_rows_per_file = 50000  # Rough estimate based on sample
    
    print(f"Estimated total transaction records: {total_transaction_files * avg_rows_per_file:,}")
    print(f"Redundant columns per transaction: {len(redundant_columns)}")
    
    # Estimate space savings (very rough)
    print(f"\nPotential normalization benefits:")
    print(f"- Remove {len(redundant_columns)} redundant columns from transaction files")
    print(f"- Keep only 'ssn' as foreign key to customers table")
    print(f"- This could reduce transaction file sizes by ~60-70%")
    print(f"- Customer data centralized in customers.csv (already done)")

def main():
    data_dir = "data_1010"
    
    if not os.path.exists(data_dir):
        print(f"Error: {data_dir} directory not found")
        sys.exit(1)
    
    # Analyze customers file
    customers_df = analyze_customers_file(os.path.join(data_dir, "customers.csv"))
    
    # Analyze merchants file
    merchants_df = analyze_merchants_file(os.path.join(data_dir, "merchants.csv"))
    
    # Analyze transaction files
    transaction_files = analyze_transaction_files(data_dir)
    
    # Calculate space savings
    if customers_df is not None:
        calculate_space_savings(data_dir, customers_df)
    
    print(f"\n=== RECOMMENDATIONS ===")
    print("1. SSN appears to be the unique identifier for customers")
    print("2. Transaction files contain redundant customer demographic data")
    print("3. Normalize by:")
    print("   - Keep customers.csv as-is (customer master data)")
    print("   - Keep merchants.csv as-is (merchant master data)")
    print("   - Remove redundant columns from transaction files:")
    print("     Remove: cc_num, first, last, gender, street, city, state, zip,")
    print("             lat, long, city_pop, job, dob, acct_num, profile")
    print("     Keep: ssn (FK), trans_num, trans_date, trans_time, unix_time,")
    print("           category, amt, is_fraud, merchant, merch_lat, merch_long")
    print("4. This will create a proper star schema with fact tables (transactions)")
    print("   and dimension tables (customers, merchants)")

if __name__ == "__main__":
    main()
