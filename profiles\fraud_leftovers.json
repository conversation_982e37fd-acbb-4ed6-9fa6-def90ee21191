{"avg_transactions_per_day": {"min": 20, "max": 25}, "date_wt": {"day_of_week": {"monday": 100, "tuesday": 100, "wednesday": 100, "thursday": 100, "friday": 125, "saturday": 175, "sunday": 150}, "time_of_year": {"holidays": {"start_date (MM-DD)": "11-30", "end_date (MM-DD)": "12-31", "weight": 200}, "post_holidays": {"start_date (MM-DD)": "1-1", "end_date (MM-DD)": "02-28", "weight": 75}, "summer": {"start_date (MM-DD)": "05-24", "end_date (MM-DD)": "09-01", "weight": 125}}, "year": {"0": 100, "1": 100, "2": 100, "3": 100}}, "categories_wt": {"food_dining": 100, "health_fitness": 100}, "categories_amt": {"gas_transport": {"mean": 60, "stdev": 15}, "grocery_net": {"mean": 40, "stdev": 20}, "grocery_pos": {"mean": 50, "stdev": 20}, "misc_net": {"mean": 50, "stdev": 150}, "misc_pos": {"mean": 50, "stdev": 150}, "shopping_net": {"mean": 50, "stdev": 300}, "shopping_pos": {"mean": 50, "stdev": 300}, "entertainment": {"mean": 50, "stdev": 50}, "food_dining": {"mean": 120, "stdev": 50}, "health_fitness": {"mean": 200, "stdev": 50}, "home": {"mean": 50, "stdev": 50}, "kids_pets": {"mean": 50, "stdev": 50}, "personal_care": {"mean": 25, "stdev": 50}, "travel": {"mean": 50, "stdev": 600}}, "shopping_time": {"AM": 50, "PM": 50}, "travel_pct": 20, "travel_max_dist": 600}